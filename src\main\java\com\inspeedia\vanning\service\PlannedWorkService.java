package com.inspeedia.vanning.service;

import com.inspeedia.vanning.domain.PlannedWork;
import com.inspeedia.vanning.repository.PlannedWorkRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * Service class for PlannedWork business logic
 *
 * This service provides business operations for PlannedWork entities with
 * reactive programming patterns and proper error handling.
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class PlannedWorkService {

    private final PlannedWorkRepository plannedWorkRepository;

    /**
     * Create a new planned work record
     */
    public Mono<PlannedWork> createPlannedWork(PlannedWork plannedWork) {
        log.debug("Creating new planned work for date: {}", plannedWork.getDate());

        plannedWork.setCreatedAt(LocalDateTime.now());
        plannedWork.setUpdatedAt(LocalDateTime.now());

        return plannedWorkRepository.save(plannedWork)
                .doOnSuccess(savedWork -> log.info("Planned work created successfully with ID: {}", savedWork.getId()))
                .doOnError(error -> log.error("Error creating planned work: {}", error.getMessage()));
    }

    /**
     * Update an existing planned work record
     */
    public Mono<PlannedWork> updatePlannedWork(Long id, PlannedWork plannedWork) {
        log.debug("Updating planned work with ID: {}", id);

        return plannedWorkRepository.findById(id)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("Planned work not found with ID: " + id)))
                .flatMap(existingWork -> {
                    // Update fields
                    existingWork.setDate(plannedWork.getDate());
                    existingWork.setVanGp(plannedWork.getVanGp());
                    existingWork.setDeliveryPlatform(plannedWork.getDeliveryPlatform());
                    existingWork.setCollectionPlatform(plannedWork.getCollectionPlatform());
                    existingWork.setLoadTime(plannedWork.getLoadTime());
                    existingWork.setSize(plannedWork.getSize());
                    existingWork.setStartTime(plannedWork.getStartTime());
                    existingWork.setEndTime(plannedWork.getEndTime());
                    existingWork.setDuration(plannedWork.getDuration());
                    existingWork.setUpdatedAt(LocalDateTime.now());

                    return plannedWorkRepository.save(existingWork);
                })
                .doOnSuccess(updatedWork -> log.info("Planned work updated successfully with ID: {}", updatedWork.getId()))
                .doOnError(error -> log.error("Error updating planned work: {}", error.getMessage()));
    }

    /**
     * Get planned work by ID
     */
    public Mono<PlannedWork> getPlannedWorkById(Long id) {
        log.debug("Fetching planned work with ID: {}", id);

        return plannedWorkRepository.findById(id)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("Planned work not found with ID: " + id)))
                .doOnSuccess(work -> log.debug("Planned work found for date: {}", work.getDate()));
    }

    /**
     * Get all active planned work with pagination
     */
    public Flux<PlannedWork> getAllActivePlannedWork(int page, int size) {
        log.debug("Fetching active planned work - page: {}, size: {}", page, size);

        long offset = (long) page * size;
        return plannedWorkRepository.findAllActive(offset, size)
                .doOnComplete(() -> log.debug("Completed fetching active planned work"));
    }

    /**
     * Get planned work by date
     */
    public Flux<PlannedWork> getPlannedWorkByDate(LocalDate date) {
        log.debug("Fetching planned work for date: {}", date);

        return plannedWorkRepository.findByDate(date);
    }

    /**
     * Get planned work by date range
     */
    public Flux<PlannedWork> getPlannedWorkByDateRange(LocalDate startDate, LocalDate endDate) {
        log.debug("Fetching planned work between {} and {}", startDate, endDate);

        return plannedWorkRepository.findByDateBetween(startDate, endDate);
    }

    /**
     * Get planned work by van GP
     */
    public Flux<PlannedWork> getPlannedWorkByVanGp(String vanGp) {
        log.debug("Fetching planned work for van GP: {}", vanGp);

        return plannedWorkRepository.findByVanGp(vanGp);
    }

    /**
     * Get planned work by delivery platform
     */
    public Flux<PlannedWork> getPlannedWorkByDeliveryPlatform(String deliveryPlatform) {
        log.debug("Fetching planned work for delivery platform: {}", deliveryPlatform);

        return plannedWorkRepository.findByDeliveryPlatform(deliveryPlatform);
    }

    /**
     * Get planned work by collection platform
     */
    public Flux<PlannedWork> getPlannedWorkByCollectionPlatform(String collectionPlatform) {
        log.debug("Fetching planned work for collection platform: {}", collectionPlatform);

        return plannedWorkRepository.findByCollectionPlatform(collectionPlatform);
    }

    /**
     * Get planned work by size
     */
    public Flux<PlannedWork> getPlannedWorkBySize(String size) {
        log.debug("Fetching planned work for size: {}", size);

        return plannedWorkRepository.findBySize(size);
    }

    /**
     * Get planned work by start time range
     */
    public Flux<PlannedWork> getPlannedWorkByStartTimeRange(LocalTime startTime, LocalTime endTime) {
        log.debug("Fetching planned work with start time between {} and {}", startTime, endTime);

        return plannedWorkRepository.findByStartTimeBetween(startTime, endTime);
    }

    /**
     * Get today's planned work
     */
    public Flux<PlannedWork> getTodaysPlannedWork() {
        log.debug("Fetching today's planned work");

        return plannedWorkRepository.findTodaysWork();
    }

    /**
     * Get upcoming planned work
     */
    public Flux<PlannedWork> getUpcomingPlannedWork() {
        log.debug("Fetching upcoming planned work");

        return plannedWorkRepository.findUpcomingWork();
    }

    /**
     * Soft delete planned work
     */
    public Mono<Void> deletePlannedWork(Long id) {
        log.debug("Soft deleting planned work with ID: {}", id);

        return plannedWorkRepository.findById(id)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("Planned work not found with ID: " + id)))
                .flatMap(work -> plannedWorkRepository.softDeleteById(id))
                .then()
                .doOnSuccess(unused -> log.info("Planned work soft deleted successfully with ID: {}", id))
                .doOnError(error -> log.error("Error deleting planned work: {}", error.getMessage()));
    }

    /**
     * Count planned work by van GP
     */
    public Mono<Long> countPlannedWorkByVanGp(String vanGp) {
        log.debug("Counting planned work for van GP: {}", vanGp);

        return plannedWorkRepository.countByVanGp(vanGp);
    }
}
