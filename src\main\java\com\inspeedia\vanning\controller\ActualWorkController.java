package com.inspeedia.vanning.controller;

import com.inspeedia.vanning.domain.ActualWork;
import com.inspeedia.vanning.service.ActualWorkService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalTime;

/**
 * REST Controller for ActualWork operations
 *
 * This controller provides reactive REST endpoints for managing actual work in
 * the VMA (Vanning Management Application) system.
 */
@Slf4j
@RestController
@RequestMapping("${app.api.base-path}/actual-work")
@RequiredArgsConstructor
@Tag(name = "Actual Work Management", description = "APIs for managing actual work records")
public class ActualWorkController {

    private final ActualWorkService actualWorkService;

    @Operation(summary = "Create a new actual work record", description = "Creates a new actual work record in the system")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Actual work created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid actual work data")
    })
    @PostMapping
    public Mono<ResponseEntity<ActualWork>> createActualWork(@Valid @RequestBody ActualWork actualWork) {
        log.info("Creating new actual work for user: {}", actualWork.getUserName());

        return actualWorkService.createActualWork(actualWork)
                .map(createdWork -> ResponseEntity.status(HttpStatus.CREATED).body(createdWork))
                .onErrorReturn(IllegalArgumentException.class, ResponseEntity.badRequest().build());
    }

    @Operation(summary = "Update an actual work record", description = "Updates an existing actual work record")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Actual work updated successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid actual work data"),
        @ApiResponse(responseCode = "404", description = "Actual work not found")
    })
    @PutMapping("/{id}")
    public Mono<ResponseEntity<ActualWork>> updateActualWork(
            @Parameter(description = "Actual work ID") @PathVariable Long id,
            @Valid @RequestBody ActualWork actualWork) {
        log.info("Updating actual work with ID: {}", id);

        return actualWorkService.updateActualWork(id, actualWork)
                .map(updatedWork -> ResponseEntity.ok(updatedWork))
                .onErrorReturn(IllegalArgumentException.class, ResponseEntity.notFound().build());
    }

    @Operation(summary = "Get actual work by ID", description = "Retrieves an actual work record by its ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Actual work found"),
        @ApiResponse(responseCode = "404", description = "Actual work not found")
    })
    @GetMapping("/{id}")
    public Mono<ResponseEntity<ActualWork>> getActualWorkById(
            @Parameter(description = "Actual work ID") @PathVariable Long id) {
        log.info("Fetching actual work with ID: {}", id);

        return actualWorkService.getActualWorkById(id)
                .map(work -> ResponseEntity.ok(work))
                .onErrorReturn(IllegalArgumentException.class, ResponseEntity.notFound().build());
    }

    @Operation(summary = "Get all active actual work", description = "Retrieves all active actual work records with pagination")
    @GetMapping
    public Flux<ActualWork> getAllActiveActualWork(
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size) {
        log.info("Fetching active actual work - page: {}, size: {}", page, size);

        return actualWorkService.getAllActiveActualWork(page, size);
    }

    @Operation(summary = "Get actual work by user name", description = "Retrieves actual work records by user name")
    @GetMapping("/user/{userName}")
    public Flux<ActualWork> getActualWorkByUserName(
            @Parameter(description = "User name") @PathVariable String userName) {
        log.info("Fetching actual work for user: {}", userName);

        return actualWorkService.getActualWorkByUserName(userName);
    }

    @Operation(summary = "Search actual work by user name", description = "Searches actual work records by user name")
    @GetMapping("/search")
    public Flux<ActualWork> searchActualWorkByUserName(
            @Parameter(description = "Search term") @RequestParam String q) {
        log.info("Searching actual work with term: {}", q);

        return actualWorkService.searchActualWorkByUserName(q);
    }

    @Operation(summary = "Get actual work by progress", description = "Retrieves actual work records by progress value")
    @GetMapping("/progress/{progress}")
    public Flux<ActualWork> getActualWorkByProgress(
            @Parameter(description = "Progress value (0-10)") @PathVariable Integer progress) {
        log.info("Fetching actual work with progress: {}", progress);

        return actualWorkService.getActualWorkByProgress(progress);
    }

    @Operation(summary = "Get actual work by progress range", description = "Retrieves actual work records by progress range")
    @GetMapping("/progress-range")
    public Flux<ActualWork> getActualWorkByProgressRange(
            @Parameter(description = "Minimum progress") @RequestParam Integer minProgress,
            @Parameter(description = "Maximum progress") @RequestParam Integer maxProgress) {
        log.info("Fetching actual work with progress between {} and {}", minProgress, maxProgress);

        return actualWorkService.getActualWorkByProgressRange(minProgress, maxProgress);
    }

    @Operation(summary = "Get high progress work", description = "Retrieves actual work records with high progress (>= 8)")
    @GetMapping("/high-progress")
    public Flux<ActualWork> getHighProgressWork() {
        log.info("Fetching high progress work");

        return actualWorkService.getHighProgressWork();
    }

    @Operation(summary = "Get low progress work", description = "Retrieves actual work records with low progress (<= 3)")
    @GetMapping("/low-progress")
    public Flux<ActualWork> getLowProgressWork() {
        log.info("Fetching low progress work");

        return actualWorkService.getLowProgressWork();
    }

    @Operation(summary = "Get actual work by start time range", description = "Retrieves actual work records by start time range")
    @GetMapping("/start-time-range")
    public Flux<ActualWork> getActualWorkByStartTimeRange(
            @Parameter(description = "Start time") @RequestParam @DateTimeFormat(pattern = "HH:mm") LocalTime startTime,
            @Parameter(description = "End time") @RequestParam @DateTimeFormat(pattern = "HH:mm") LocalTime endTime) {
        log.info("Fetching actual work with start time between {} and {}", startTime, endTime);

        return actualWorkService.getActualWorkByStartTimeRange(startTime, endTime);
    }

    @Operation(summary = "Delete an actual work record", description = "Soft deletes an actual work record")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Actual work deleted successfully"),
        @ApiResponse(responseCode = "404", description = "Actual work not found")
    })
    @DeleteMapping("/{id}")
    public Mono<ResponseEntity<Void>> deleteActualWork(
            @Parameter(description = "Actual work ID") @PathVariable Long id) {
        log.info("Deleting actual work with ID: {}", id);

        return actualWorkService.deleteActualWork(id)
                .then(Mono.just(ResponseEntity.noContent().<Void>build()))
                .onErrorReturn(IllegalArgumentException.class, ResponseEntity.notFound().build());
    }

    @Operation(summary = "Count actual work by user name", description = "Returns the count of actual work records by user name")
    @GetMapping("/count/user/{userName}")
    public Mono<Long> countActualWorkByUserName(
            @Parameter(description = "User name") @PathVariable String userName) {
        log.info("Counting actual work for user: {}", userName);

        return actualWorkService.countActualWorkByUserName(userName);
    }
}
