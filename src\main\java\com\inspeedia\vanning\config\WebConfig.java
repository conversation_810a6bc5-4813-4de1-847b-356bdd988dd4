package com.inspeedia.vanning.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.reactive.CorsWebFilter;
import org.springframework.web.cors.reactive.UrlBasedCorsConfigurationSource;
import org.springframework.web.reactive.config.EnableWebFlux;
import org.springframework.web.reactive.config.WebFluxConfigurer;

/**
 * Web configuration for reactive Spring WebFlux
 *
 * This configuration class sets up CORS, content negotiation, and other
 * web-related configurations.
 */
@Slf4j
@Configuration
@EnableWebFlux
@RequiredArgsConstructor
public class WebConfig implements WebFluxConfigurer {

    private final AppProperties appProperties;

    /**
     * Configures CORS for the application
     */
    @Bean
    public CorsWebFilter corsWebFilter() {
        CorsConfiguration corsConfig = new CorsConfiguration();
        corsConfig.setAllowedOriginPatterns(appProperties.getCors().getAllowedOrigins());
        corsConfig.setAllowedMethods(appProperties.getCors().getAllowedMethods());
        corsConfig.setAllowedHeaders(appProperties.getCors().getAllowedHeaders());
        corsConfig.setAllowCredentials(appProperties.getCors().isAllowCredentials());
        corsConfig.setMaxAge(appProperties.getCors().getMaxAge());

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", corsConfig);

        log.info("CORS configured with origins: {}, methods: {}",
                appProperties.getCors().getAllowedOrigins(),
                appProperties.getCors().getAllowedMethods());
        return new CorsWebFilter(source);
    }
}
